import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { getRedisClient } from 'src/cache/redis.init';
import { RedisClientType } from 'redis';
import { QueueInstance } from 'src/queue-system';
import { NotificationQueueName } from 'src/queue-system/predefined-data';

interface DelayedJob {
  id: string;
  payload: any;
  queue: string;
  executeAt: number;
  retryCount?: number;
  maxRetries?: number;
}

@Injectable()
export class DelayedJobService implements OnModuleInit, OnModuleDestroy {
  private redisClient: RedisClientType;
  private isProcessing = false;
  private processingInterval: NodeJS.Timeout;
  private readonly jobKeyPrefix = 'delayed_job:';
  private readonly jobQueueKey = 'delayed_jobs_queue';
  private readonly processingIntervalMs = 30000; // Check every 30 seconds
  private readonly maxRetries = 3;

  async onModuleInit() {
    try {
      this.redisClient = await getRedisClient();
      this.startProcessing();
      console.log('🚀 [DELAYED-JOB] Service initialized with Redis backend');
    } catch (error) {
      console.error('❌ [DELAYED-JOB] Failed to initialize:', error.message);
    }
  }

  async onModuleDestroy() {
    this.stopProcessing();
    console.log('🛑 [DELAYED-JOB] Service stopped');
  }

  /**
   * Schedule a delayed job
   */
  async scheduleJob(
    jobId: string,
    payload: any,
    queue: string,
    delayMs: number,
    maxRetries: number = this.maxRetries
  ): Promise<boolean> {
    try {
      const executeAt = Date.now() + delayMs;
      const job: DelayedJob = {
        id: jobId,
        payload,
        queue,
        executeAt,
        retryCount: 0,
        maxRetries,
      };

      // Store job data
      const jobKey = `${this.jobKeyPrefix}${jobId}`;
      await this.redisClient.setEx(
        jobKey,
        Math.ceil(delayMs / 1000) + 300, // TTL with 5min buffer
        JSON.stringify(job)
      );

      // Add to sorted set for time-based processing
      await this.redisClient.zAdd(this.jobQueueKey, {
        score: executeAt,
        value: jobId,
      });

      const delayMinutes = Math.round(delayMs / 60000);
      console.log(`⏰ [DELAYED-JOB] Scheduled job ${jobId} for ${delayMinutes}min delay`);
      return true;
    } catch (error) {
      console.error(`❌ [DELAYED-JOB] Failed to schedule job ${jobId}:`, error.message);
      return false;
    }
  }

  /**
   * Cancel a scheduled job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    try {
      const jobKey = `${this.jobKeyPrefix}${jobId}`;
      
      // Remove from both storage and queue
      const [delResult, remResult] = await Promise.all([
        this.redisClient.del(jobKey),
        this.redisClient.zRem(this.jobQueueKey, jobId),
      ]);

      if (delResult > 0 || remResult > 0) {
        console.log(`🗑️ [DELAYED-JOB] Cancelled job ${jobId}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ [DELAYED-JOB] Failed to cancel job ${jobId}:`, error.message);
      return false;
    }
  }

  /**
   * Start the job processing loop
   */
  private startProcessing() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    this.processingInterval = setInterval(() => {
      this.processReadyJobs().catch(error => {
        console.error('❌ [DELAYED-JOB] Processing error:', error.message);
      });
    }, this.processingIntervalMs);
  }

  /**
   * Stop the job processing loop
   */
  private stopProcessing() {
    this.isProcessing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
  }

  /**
   * Process jobs that are ready to execute
   */
  private async processReadyJobs() {
    try {
      const now = Date.now();
      
      // Get jobs ready for execution
      const readyJobIds = await this.redisClient.zRangeByScore(
        this.jobQueueKey,
        0,
        now,
        { LIMIT: { offset: 0, count: 50 } } // Process up to 50 jobs at once
      );

      if (readyJobIds.length === 0) {
        return;
      }

      console.log(`🔄 [DELAYED-JOB] Processing ${readyJobIds.length} ready jobs`);

      // Process each job
      for (const jobId of readyJobIds) {
        await this.executeJob(jobId);
      }
    } catch (error) {
      console.error('❌ [DELAYED-JOB] Error processing ready jobs:', error.message);
    }
  }

  /**
   * Execute a specific job
   */
  private async executeJob(jobId: string) {
    try {
      const jobKey = `${this.jobKeyPrefix}${jobId}`;
      const jobData = await this.redisClient.get(jobKey);
      
      if (!jobData) {
        // Job data not found, remove from queue
        await this.redisClient.zRem(this.jobQueueKey, jobId);
        return;
      }

      const job: DelayedJob = JSON.parse(jobData);
      
      try {
        // Execute the job by sending to queue
        const queueInstance = await QueueInstance;
        await queueInstance.sendPayload(
          job.queue,
          Buffer.from(JSON.stringify(job.payload))
        );

        // Job executed successfully, clean up
        await this.cleanupJob(jobId);
        console.log(`✅ [DELAYED-JOB] Executed job ${jobId} successfully`);
      } catch (executionError) {
        // Handle job execution failure
        await this.handleJobFailure(job, executionError);
      }
    } catch (error) {
      console.error(`❌ [DELAYED-JOB] Error executing job ${jobId}:`, error.message);
      // Remove problematic job from queue
      await this.redisClient.zRem(this.jobQueueKey, jobId);
    }
  }

  /**
   * Handle job execution failure with retry logic
   */
  private async handleJobFailure(job: DelayedJob, error: any) {
    const retryCount = (job.retryCount || 0) + 1;
    
    if (retryCount <= job.maxRetries) {
      // Retry with exponential backoff
      const retryDelayMs = Math.min(30000 * Math.pow(2, retryCount - 1), 300000); // Max 5 minutes
      const newExecuteAt = Date.now() + retryDelayMs;
      
      job.retryCount = retryCount;
      job.executeAt = newExecuteAt;
      
      // Update job data
      const jobKey = `${this.jobKeyPrefix}${job.id}`;
      await this.redisClient.setEx(
        jobKey,
        Math.ceil(retryDelayMs / 1000) + 300,
        JSON.stringify(job)
      );
      
      // Update queue score
      await this.redisClient.zAdd(this.jobQueueKey, {
        score: newExecuteAt,
        value: job.id,
      });
      
      console.log(`🔄 [DELAYED-JOB] Retrying job ${job.id} (attempt ${retryCount}/${job.maxRetries}) in ${Math.round(retryDelayMs / 1000)}s`);
    } else {
      // Max retries exceeded, clean up
      await this.cleanupJob(job.id);
      console.error(`❌ [DELAYED-JOB] Job ${job.id} failed after ${job.maxRetries} retries:`, error.message);
    }
  }

  /**
   * Clean up job data after successful execution or max retries
   */
  private async cleanupJob(jobId: string) {
    const jobKey = `${this.jobKeyPrefix}${jobId}`;
    await Promise.all([
      this.redisClient.del(jobKey),
      this.redisClient.zRem(this.jobQueueKey, jobId),
    ]);
  }

  /**
   * Get job statistics
   */
  async getJobStats(): Promise<{
    totalJobs: number;
    readyJobs: number;
    futureJobs: number;
  }> {
    try {
      const now = Date.now();
      const totalJobs = await this.redisClient.zCard(this.jobQueueKey);
      const readyJobs = await this.redisClient.zCount(this.jobQueueKey, 0, now);
      const futureJobs = totalJobs - readyJobs;

      return { totalJobs, readyJobs, futureJobs };
    } catch (error) {
      console.error('❌ [DELAYED-JOB] Error getting stats:', error.message);
      return { totalJobs: 0, readyJobs: 0, futureJobs: 0 };
    }
  }

  /**
   * Clear all jobs (for testing/maintenance)
   */
  async clearAllJobs(): Promise<number> {
    try {
      const jobIds = await this.redisClient.zRange(this.jobQueueKey, 0, -1);
      const jobKeys = jobIds.map(id => `${this.jobKeyPrefix}${id}`);
      
      const deletedCount = await this.redisClient.del([this.jobQueueKey, ...jobKeys]);
      console.log(`🗑️ [DELAYED-JOB] Cleared ${deletedCount} jobs`);
      return deletedCount;
    } catch (error) {
      console.error('❌ [DELAYED-JOB] Error clearing jobs:', error.message);
      return 0;
    }
  }
}
