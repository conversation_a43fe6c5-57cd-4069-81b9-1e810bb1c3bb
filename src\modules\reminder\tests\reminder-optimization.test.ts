import { Test, TestingModule } from '@nestjs/testing';
import { ReminderService } from '../services/reminder.service';
import { ReminderSchedulerService } from '../services/reminder-scheduler.service';
import { DelayedJobService } from '../services/delayed-job.service';
import { PerformanceMonitorService } from '../services/performance-monitor.service';
import { ReminderRepository } from '../repositories/reminder.repository';
import { NotificationStatsService } from '../../notification/services/notification-stats.service';
import { Reminder } from 'src/entity/reminder';

// Mock Redis client
const mockRedisClient = {
  setEx: jest.fn().mockResolvedValue('OK'),
  get: jest.fn().mockResolvedValue(null),
  del: jest.fn().mockResolvedValue(1),
  zAdd: jest.fn().mockResolvedValue(1),
  zRem: jest.fn().mockResolvedValue(1),
  zCard: jest.fn().mockResolvedValue(0),
  zRange: jest.fn().mockResolvedValue([]),
  zRangeByScore: jest.fn().mockResolvedValue([]),
  hSet: jest.fn().mockResolvedValue(1),
  hGet: jest.fn().mockResolvedValue(null),
  hKeys: jest.fn().mockResolvedValue([]),
  hDel: jest.fn().mockResolvedValue(1),
  set: jest.fn().mockResolvedValue('OK'),
};

// Mock getRedisClient
jest.mock('src/cache/redis.init', () => ({
  getRedisClient: jest.fn().mockResolvedValue(mockRedisClient),
}));

// Mock QueueInstance
const mockQueueInstance = {
  sendPayload: jest.fn().mockResolvedValue(true),
};

jest.mock('src/queue-system', () => ({
  QueueInstance: Promise.resolve(mockQueueInstance),
}));

describe('Reminder Optimization Tests', () => {
  let reminderService: ReminderService;
  let reminderSchedulerService: ReminderSchedulerService;
  let delayedJobService: DelayedJobService;
  let performanceMonitorService: PerformanceMonitorService;
  let reminderRepository: ReminderRepository;
  let notificationStatsService: NotificationStatsService;

  const mockReminders: Partial<Reminder>[] = [
    {
      id: '1',
      title: 'Morning Workout',
      message: 'Time for your morning workout!',
      time: '07:00',
      isActive: true,
      userId: 'user1',
    },
    {
      id: '2',
      title: 'Take Vitamins',
      message: 'Remember to take your vitamins',
      time: '07:05',
      isActive: true,
      userId: 'user1',
    },
    {
      id: '3',
      title: 'Lunch Break',
      message: 'Time for lunch!',
      time: '12:00',
      isActive: true,
      userId: 'user2',
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReminderService,
        ReminderSchedulerService,
        DelayedJobService,
        PerformanceMonitorService,
        {
          provide: ReminderRepository,
          useValue: {
            findActiveRemindersForTime: jest.fn(),
            findActiveRemindersForTimes: jest.fn(),
            findAllActiveReminders: jest.fn(),
            createReminder: jest.fn(),
            updateReminder: jest.fn(),
            deleteReminder: jest.fn(),
          },
        },
        {
          provide: NotificationStatsService,
          useValue: {
            generateBatchId: jest.fn().mockReturnValue('test-batch-id'),
            startBatch: jest.fn(),
            incrementSent: jest.fn(),
            incrementFailed: jest.fn(),
          },
        },
      ],
    }).compile();

    reminderService = module.get<ReminderService>(ReminderService);
    reminderSchedulerService = module.get<ReminderSchedulerService>(ReminderSchedulerService);
    delayedJobService = module.get<DelayedJobService>(DelayedJobService);
    performanceMonitorService = module.get<PerformanceMonitorService>(PerformanceMonitorService);
    reminderRepository = module.get<ReminderRepository>(ReminderRepository);
    notificationStatsService = module.get<NotificationStatsService>(NotificationStatsService);

    // Initialize services
    await delayedJobService.onModuleInit();
    await performanceMonitorService.onModuleInit();
  });

  afterEach(async () => {
    await delayedJobService.onModuleDestroy();
    await performanceMonitorService.onModuleDestroy();
    jest.clearAllMocks();
  });

  describe('Intelligent Batching Strategy', () => {
    it('should group reminders by time proximity correctly', async () => {
      const reminders = mockReminders.slice(0, 2) as Reminder[]; // 07:00 and 07:05
      jest.spyOn(reminderRepository, 'findActiveRemindersForTime').mockResolvedValue(reminders);

      await reminderService.sendReminderNotifications();

      // Should batch reminders within 5-minute window
      expect(mockQueueInstance.sendPayload).toHaveBeenCalledTimes(1);
      expect(notificationStatsService.incrementSent).toHaveBeenCalledTimes(2);
    });

    it('should handle lookahead processing for future reminders', async () => {
      const currentTimeReminders = [mockReminders[0]] as Reminder[];
      const lookaheadReminders = [mockReminders[1]] as Reminder[];
      
      jest.spyOn(reminderRepository, 'findActiveRemindersForTimes').mockResolvedValue(
        new Map([
          ['07:00', currentTimeReminders],
          ['07:05', lookaheadReminders],
        ])
      );

      // Mock current time to be 07:00
      jest.spyOn(Date.prototype, 'getHours').mockReturnValue(7);
      jest.spyOn(Date.prototype, 'getMinutes').mockReturnValue(0);

      await reminderService.sendReminderNotifications();

      // Should process current time reminders immediately and schedule lookahead
      expect(mockQueueInstance.sendPayload).toHaveBeenCalled();
      expect(mockRedisClient.zAdd).toHaveBeenCalled(); // Delayed job scheduling
    });

    it('should optimize batch sizes for performance', async () => {
      const manyReminders = Array.from({ length: 20 }, (_, i) => ({
        ...mockReminders[0],
        id: `reminder-${i}`,
        title: `Reminder ${i}`,
      })) as Reminder[];

      jest.spyOn(reminderRepository, 'findActiveRemindersForTime').mockResolvedValue(manyReminders);

      await reminderService.sendReminderNotifications();

      // Should group reminders efficiently (max 5 per group)
      const callCount = mockQueueInstance.sendPayload.mock.calls.length;
      expect(callCount).toBeGreaterThanOrEqual(4); // At least 4 groups for 20 reminders
    });
  });

  describe('Redis-based Delayed Job System', () => {
    it('should schedule delayed jobs successfully', async () => {
      const reminder = mockReminders[0] as Reminder;
      const delayMs = 60000; // 1 minute

      const success = await delayedJobService.scheduleJob(
        'test-job-id',
        { test: 'payload' },
        'test-queue',
        delayMs
      );

      expect(success).toBe(true);
      expect(mockRedisClient.setEx).toHaveBeenCalled();
      expect(mockRedisClient.zAdd).toHaveBeenCalled();
    });

    it('should cancel scheduled jobs', async () => {
      const jobId = 'test-job-id';

      const success = await delayedJobService.cancelJob(jobId);

      expect(mockRedisClient.del).toHaveBeenCalled();
      expect(mockRedisClient.zRem).toHaveBeenCalled();
    });

    it('should process ready jobs correctly', async () => {
      const jobId = 'ready-job';
      const jobData = {
        id: jobId,
        payload: { test: 'data' },
        queue: 'test-queue',
        executeAt: Date.now() - 1000, // 1 second ago
        retryCount: 0,
        maxRetries: 3,
      };

      mockRedisClient.zRangeByScore.mockResolvedValue([jobId]);
      mockRedisClient.get.mockResolvedValue(JSON.stringify(jobData));

      // Trigger job processing manually
      await delayedJobService['processReadyJobs']();

      expect(mockQueueInstance.sendPayload).toHaveBeenCalledWith(
        'test-queue',
        expect.any(Buffer)
      );
    });

    it('should handle job failures with retry logic', async () => {
      const jobId = 'failing-job';
      const jobData = {
        id: jobId,
        payload: { test: 'data' },
        queue: 'test-queue',
        executeAt: Date.now() - 1000,
        retryCount: 0,
        maxRetries: 3,
      };

      mockRedisClient.zRangeByScore.mockResolvedValue([jobId]);
      mockRedisClient.get.mockResolvedValue(JSON.stringify(jobData));
      mockQueueInstance.sendPayload.mockRejectedValue(new Error('Queue error'));

      await delayedJobService['processReadyJobs']();

      // Should retry the job
      expect(mockRedisClient.setEx).toHaveBeenCalled(); // Update job with retry count
      expect(mockRedisClient.zAdd).toHaveBeenCalled(); // Reschedule with delay
    });
  });

  describe('Adaptive Scheduling System', () => {
    it('should adjust scheduling frequency based on system load', async () => {
      // Mock high CPU usage
      jest.spyOn(reminderSchedulerService as any, 'getCurrentSystemLoad').mockResolvedValue({
        cpuUsage: 85,
        memoryUsage: 70,
      });

      await reminderSchedulerService.adjustSchedulingFrequency();

      // Should increase interval during high load
      const currentInterval = reminderSchedulerService['currentIntervalMinutes'];
      expect(currentInterval).toBeGreaterThan(5); // Should be higher than minimum
    });

    it('should optimize for off-peak hours', async () => {
      // Mock off-peak time (3 AM)
      jest.spyOn(Date.prototype, 'getHours').mockReturnValue(3);
      jest.spyOn(Date.prototype, 'getMinutes').mockReturnValue(0);

      await reminderSchedulerService.adjustSchedulingFrequency();

      // Should use longer intervals during off-peak
      const currentInterval = reminderSchedulerService['currentIntervalMinutes'];
      expect(currentInterval).toBeGreaterThanOrEqual(15); // Off-peak optimization
    });
  });

  describe('Performance Monitoring', () => {
    it('should track reminder operations correctly', () => {
      performanceMonitorService.trackReminderSent(5);
      performanceMonitorService.trackReminderScheduled(3);
      performanceMonitorService.trackBatchProcessed();
      performanceMonitorService.trackResponseTime(150);
      performanceMonitorService.trackError();

      // Verify internal counters are updated
      expect(performanceMonitorService['remindersSentCount']).toBe(5);
      expect(performanceMonitorService['remindersScheduledCount']).toBe(3);
      expect(performanceMonitorService['batchesProcessedCount']).toBe(1);
      expect(performanceMonitorService['responseTimeSum']).toBe(150);
      expect(performanceMonitorService['errorCount']).toBe(1);
    });

    it('should assess system health correctly', async () => {
      const metrics = {
        timestamp: Date.now(),
        cpuUsage: 85, // High CPU
        memoryUsage: 60,
        remindersSent: 100,
        remindersScheduled: 50,
        batchesProcessed: 10,
        averageResponseTime: 200,
        errorRate: 2,
        queueDepth: 5,
        systemLoad: 'high' as const,
      };

      const health = performanceMonitorService['assessSystemHealth'](metrics);

      expect(health.status).toBe('degraded'); // High CPU should trigger degraded status
      expect(health.cpuHealth).toBe('critical');
      expect(health.recommendations).toContain('Reduce reminder scheduling frequency');
    });

    it('should generate appropriate recommendations', async () => {
      const criticalMetrics = {
        timestamp: Date.now(),
        cpuUsage: 95,
        memoryUsage: 90,
        remindersSent: 1000,
        remindersScheduled: 500,
        batchesProcessed: 100,
        averageResponseTime: 6000, // Slow response
        errorRate: 15, // High error rate
        queueDepth: 1500, // High queue depth
        systemLoad: 'critical' as const,
      };

      const health = performanceMonitorService['assessSystemHealth'](criticalMetrics);

      expect(health.recommendations).toContain('Reduce reminder scheduling frequency');
      expect(health.recommendations).toContain('Clear cache and optimize memory usage');
      expect(health.recommendations).toContain('Investigate error sources');
      expect(health.recommendations).toContain('Queue depth is high - consider increasing processing capacity');
      expect(health.recommendations).toContain('Response times are slow - optimize database queries');
    });
  });

  describe('Database Query Optimization', () => {
    it('should use batch queries for multiple time lookups', async () => {
      const times = ['07:00', '07:05', '07:10'];
      const reminderMap = new Map([
        ['07:00', [mockReminders[0]] as Reminder[]],
        ['07:05', [mockReminders[1]] as Reminder[]],
        ['07:10', []],
      ]);

      jest.spyOn(reminderRepository, 'findActiveRemindersForTimes').mockResolvedValue(reminderMap);

      const result = await reminderRepository.findActiveRemindersForTimes(times);

      expect(reminderRepository.findActiveRemindersForTimes).toHaveBeenCalledWith(times);
      expect(result.size).toBe(3);
      expect(result.get('07:00')).toHaveLength(1);
      expect(result.get('07:10')).toHaveLength(0);
    });
  });

  describe('Performance Benchmarks', () => {
    it('should process large batches efficiently', async () => {
      const largeReminderSet = Array.from({ length: 1000 }, (_, i) => ({
        id: `reminder-${i}`,
        title: `Reminder ${i}`,
        message: `Message ${i}`,
        time: '07:00',
        isActive: true,
        userId: `user-${i % 100}`, // 100 different users
      })) as Reminder[];

      jest.spyOn(reminderRepository, 'findActiveRemindersForTime').mockResolvedValue(largeReminderSet);

      const startTime = Date.now();
      await reminderService.sendReminderNotifications();
      const processingTime = Date.now() - startTime;

      // Should process 1000 reminders in under 5 seconds
      expect(processingTime).toBeLessThan(5000);

      // Should use intelligent batching (not send 1000 individual notifications)
      const queueCalls = mockQueueInstance.sendPayload.mock.calls.length;
      expect(queueCalls).toBeLessThan(200); // Should batch efficiently
    });

    it('should maintain performance under concurrent load', async () => {
      const reminders = mockReminders as Reminder[];
      jest.spyOn(reminderRepository, 'findActiveRemindersForTime').mockResolvedValue(reminders);

      // Simulate concurrent requests
      const concurrentRequests = Array.from({ length: 10 }, () =>
        reminderService.sendReminderNotifications()
      );

      const startTime = Date.now();
      await Promise.all(concurrentRequests);
      const totalTime = Date.now() - startTime;

      // Should handle 10 concurrent requests efficiently
      expect(totalTime).toBeLessThan(3000);
    });

    it('should demonstrate memory efficiency with caching', async () => {
      const times = ['07:00', '07:05', '07:10', '07:15', '07:20'];
      const reminderMap = new Map(times.map(time => [time, mockReminders as Reminder[]]));

      jest.spyOn(reminderRepository, 'findActiveRemindersForTimes').mockResolvedValue(reminderMap);

      // First call - should hit database
      await reminderRepository.findActiveRemindersForTimes(times);

      // Second call - should use cache (in real implementation)
      await reminderRepository.findActiveRemindersForTimes(times);

      // Verify batch query is used instead of individual queries
      expect(reminderRepository.findActiveRemindersForTimes).toHaveBeenCalledTimes(2);
      expect(reminderRepository.findActiveRemindersForTimes).toHaveBeenCalledWith(times);
    });
  });

  describe('End-to-End Integration', () => {
    it('should handle complete reminder workflow with optimizations', async () => {
      const reminders = mockReminders as Reminder[];
      jest.spyOn(reminderRepository, 'findActiveRemindersForTimes').mockResolvedValue(
        new Map([['07:00', reminders]])
      );

      // Mock current time
      jest.spyOn(Date.prototype, 'getHours').mockReturnValue(7);
      jest.spyOn(Date.prototype, 'getMinutes').mockReturnValue(0);

      await reminderService.sendReminderNotifications();

      // Verify all optimizations are working together
      expect(mockQueueInstance.sendPayload).toHaveBeenCalled(); // Notifications sent
      expect(notificationStatsService.startBatch).toHaveBeenCalled(); // Batch tracking
      expect(mockRedisClient.hSet).toHaveBeenCalled(); // Performance metrics stored
    });

    it('should maintain 100% delivery reliability', async () => {
      const reminders = mockReminders as Reminder[];
      jest.spyOn(reminderRepository, 'findActiveRemindersForTime').mockResolvedValue(reminders);

      // Simulate some queue failures
      mockQueueInstance.sendPayload
        .mockResolvedValueOnce(true)  // First batch succeeds
        .mockRejectedValueOnce(new Error('Queue error'))  // Second batch fails
        .mockResolvedValueOnce(true); // Third batch succeeds

      await reminderService.sendReminderNotifications();

      // Should track both successful and failed sends
      expect(notificationStatsService.incrementSent).toHaveBeenCalled();
      expect(notificationStatsService.incrementFailed).toHaveBeenCalled();

      // Failed reminders should be tracked for retry
      expect(performanceMonitorService['errorCount']).toBeGreaterThan(0);
    });
  });
});
