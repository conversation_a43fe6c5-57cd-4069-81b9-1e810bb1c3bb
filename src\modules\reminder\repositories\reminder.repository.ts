import { Injectable } from '@nestjs/common';
import { ReminderModel } from 'src/database/reminder/reminder.model';
import { <PERSON>mind<PERSON> } from 'src/entity/reminder';
import { CreateReminderDto, UpdateReminderDto } from '../rest/dto/reminder.dto';

interface CacheEntry {
  data: Reminder[];
  timestamp: number;
  ttl: number;
}

@Injectable()
export class ReminderRepository {
  private readonly cache = new Map<string, CacheEntry>();
  private readonly defaultCacheTtl = 2 * 60 * 1000; // 2 minutes
  private readonly activeCacheTtl = 5 * 60 * 1000; // 5 minutes for active reminders
  async createReminder(
    adminId: string,
    reminderData: CreateReminderDto,
  ): Promise<Reminder> {
    try {
      const reminder = new ReminderModel({
        userId: adminId, // Store admin ID who created the reminder
        ...reminderData,
      });
      const savedReminder = await reminder.save();

      // Clear relevant cache
      this.clearRelevantCache(savedReminder);

      return savedReminder;
    } catch (error) {
      console.error('Error creating reminder:', error.message);
      return null;
    }
  }

  async findReminderById(id: string): Promise<Reminder> {
    try {
      return await ReminderModel.findOne({ id }).lean();
    } catch (error) {
      console.error('Error finding reminder by ID:', error.message);
      return null;
    }
  }

  async findRemindersByUserId(userId: string): Promise<Reminder[]> {
    try {
      return await ReminderModel.find({ userId }).lean();
    } catch (error) {
      console.error('Error finding reminders by user ID:', error.message);
      return [];
    }
  }

  async findAllReminders(): Promise<Reminder[]> {
    try {
      return await ReminderModel.find({})
        .select('id userId title message time isActive isRepeating repeatDays')
        .lean();
    } catch (error) {
      console.error('Error finding all reminders:', error.message);
      return [];
    }
  }

  /**
   * Get all active reminders with caching
   */
  async findAllActiveReminders(): Promise<Reminder[]> {
    const cacheKey = 'active_reminders';
    const cached = this.getFromCache(cacheKey);

    if (cached) {
      return cached;
    }

    try {
      const reminders = await ReminderModel.find({ isActive: true })
        .select('id userId title message time isActive isRepeating repeatDays')
        .lean();

      this.setCache(cacheKey, reminders, this.activeCacheTtl);
      return reminders;
    } catch (error) {
      console.error('Error finding active reminders:', error.message);
      return [];
    }
  }

  async updateReminder(
    id: string,
    updateData: UpdateReminderDto,
  ): Promise<Reminder> {
    try {
      // Get old reminder for cache clearing
      const oldReminder = await ReminderModel.findOne({ id }).lean();

      const updatedReminder = await ReminderModel.findOneAndUpdate(
        { id },
        { $set: updateData },
        { new: true },
      ).lean();

      // Clear cache for both old and new reminder times
      if (oldReminder) {
        this.clearRelevantCache(oldReminder);
      }
      if (updatedReminder) {
        this.clearRelevantCache(updatedReminder);
      }

      return updatedReminder;
    } catch (error) {
      console.error('Error updating reminder:', error.message);
      return null;
    }
  }

  async deleteReminder(id: string): Promise<boolean> {
    try {
      // Get reminder before deletion for cache clearing
      const reminder = await ReminderModel.findOne({ id }).lean();

      const result = await ReminderModel.deleteOne({ id });

      if (result.deletedCount > 0 && reminder) {
        this.clearRelevantCache(reminder);
      }

      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error deleting reminder:', error.message);
      return false;
    }
  }

  async findActiveRemindersForTime(time: string): Promise<Reminder[]> {
    const currentDay = new Date()
      .toLocaleDateString('en-US', { weekday: 'long' })
      .toLowerCase();

    const cacheKey = `reminders_${time}_${currentDay}`;
    const cached = this.getFromCache(cacheKey);

    if (cached) {
      return cached;
    }

    try {
      // Optimized query using compound index
      const reminders = await ReminderModel.find({
        time,
        isActive: true,
        $or: [
          { isRepeating: false },
          {
            isRepeating: true,
            repeatDays: currentDay,
          },
        ],
      })
      .select('id userId title message time isActive isRepeating repeatDays')
      .lean();

      this.setCache(cacheKey, reminders, this.defaultCacheTtl);
      return reminders;
    } catch (error) {
      console.error('Error finding active reminders for time:', error.message);
      return [];
    }
  }

  /**
   * Batch find reminders for multiple times (for lookahead processing)
   */
  async findActiveRemindersForTimes(times: string[]): Promise<Map<string, Reminder[]>> {
    const currentDay = new Date()
      .toLocaleDateString('en-US', { weekday: 'long' })
      .toLowerCase();

    const result = new Map<string, Reminder[]>();
    const uncachedTimes: string[] = [];

    // Check cache first
    for (const time of times) {
      const cacheKey = `reminders_${time}_${currentDay}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        result.set(time, cached);
      } else {
        uncachedTimes.push(time);
      }
    }

    if (uncachedTimes.length === 0) {
      return result;
    }

    try {
      // Batch query for uncached times
      const reminders = await ReminderModel.find({
        time: { $in: uncachedTimes },
        isActive: true,
        $or: [
          { isRepeating: false },
          {
            isRepeating: true,
            repeatDays: currentDay,
          },
        ],
      })
      .select('id userId title message time isActive isRepeating repeatDays')
      .lean();

      // Group by time and cache
      const groupedByTime = new Map<string, Reminder[]>();
      for (const reminder of reminders) {
        if (!groupedByTime.has(reminder.time)) {
          groupedByTime.set(reminder.time, []);
        }
        groupedByTime.get(reminder.time)!.push(reminder);
      }

      // Cache and add to result
      for (const time of uncachedTimes) {
        const timeReminders = groupedByTime.get(time) || [];
        const cacheKey = `reminders_${time}_${currentDay}`;
        this.setCache(cacheKey, timeReminders, this.defaultCacheTtl);
        result.set(time, timeReminders);
      }

      return result;
    } catch (error) {
      console.error('Error finding active reminders for times:', error.message);
      // Return partial results from cache
      return result;
    }
  }

  /**
   * Cache management methods
   */
  private getFromCache(key: string): Reminder[] | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  private setCache(key: string, data: Reminder[], ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });

    // Clean up expired entries periodically
    if (this.cache.size > 100) {
      this.cleanupCache();
    }
  }

  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Clear cache when reminders are modified
   */
  private clearRelevantCache(reminder?: Reminder): void {
    if (reminder) {
      // Clear specific time-based cache
      const currentDay = new Date()
        .toLocaleDateString('en-US', { weekday: 'long' })
        .toLowerCase();
      const cacheKey = `reminders_${reminder.time}_${currentDay}`;
      this.cache.delete(cacheKey);
    }

    // Clear active reminders cache
    this.cache.delete('active_reminders');
  }
}
