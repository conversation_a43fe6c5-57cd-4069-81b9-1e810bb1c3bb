import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { getRedisClient } from 'src/cache/redis.init';
import { RedisClientType } from 'redis';
import * as os from 'os';

interface PerformanceMetrics {
  timestamp: number;
  cpuUsage: number;
  memoryUsage: number;
  remindersSent: number;
  remindersScheduled: number;
  batchesProcessed: number;
  averageResponseTime: number;
  errorRate: number;
  queueDepth: number;
  systemLoad: 'low' | 'medium' | 'high' | 'critical';
}

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'critical';
  cpuHealth: 'good' | 'warning' | 'critical';
  memoryHealth: 'good' | 'warning' | 'critical';
  performanceHealth: 'good' | 'warning' | 'critical';
  recommendations: string[];
}

@Injectable()
export class PerformanceMonitorService implements OnModuleInit, OnModuleDestroy {
  private redisClient: RedisClientType;
  private monitoringInterval: NodeJS.Timeout;
  private readonly metricsKey = 'reminder_performance_metrics';
  private readonly healthKey = 'reminder_system_health';
  private readonly monitoringIntervalMs = 60000; // 1 minute
  private readonly metricsRetentionHours = 24;
  
  // Performance counters
  private remindersSentCount = 0;
  private remindersScheduledCount = 0;
  private batchesProcessedCount = 0;
  private responseTimeSum = 0;
  private responseTimeCount = 0;
  private errorCount = 0;
  private operationCount = 0;

  async onModuleInit() {
    try {
      this.redisClient = await getRedisClient();
      this.startMonitoring();
      console.log('📊 [PERFORMANCE-MONITOR] Service initialized');
    } catch (error) {
      console.error('❌ [PERFORMANCE-MONITOR] Failed to initialize:', error.message);
    }
  }

  async onModuleDestroy() {
    this.stopMonitoring();
    console.log('🛑 [PERFORMANCE-MONITOR] Service stopped');
  }

  /**
   * Start performance monitoring
   */
  private startMonitoring() {
    this.monitoringInterval = setInterval(() => {
      this.collectAndStoreMetrics().catch(error => {
        console.error('❌ [PERFORMANCE-MONITOR] Error collecting metrics:', error.message);
      });
    }, this.monitoringIntervalMs);
  }

  /**
   * Stop performance monitoring
   */
  private stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
  }

  /**
   * Collect and store performance metrics
   */
  private async collectAndStoreMetrics() {
    try {
      const metrics = await this.collectCurrentMetrics();
      const health = this.assessSystemHealth(metrics);
      
      // Store metrics in Redis with timestamp
      const metricsWithId = { ...metrics, id: `metrics_${Date.now()}` };
      await this.redisClient.hSet(this.metricsKey, metrics.timestamp.toString(), JSON.stringify(metricsWithId));
      await this.redisClient.set(this.healthKey, JSON.stringify(health), { EX: 300 }); // 5 min TTL
      
      // Clean up old metrics
      await this.cleanupOldMetrics();
      
      // Log health status if not healthy
      if (health.status !== 'healthy') {
        console.warn(`⚠️ [PERFORMANCE-MONITOR] System health: ${health.status.toUpperCase()}`, health.recommendations);
      }
      
      // Reset counters for next interval
      this.resetCounters();
    } catch (error) {
      console.error('❌ [PERFORMANCE-MONITOR] Error in metrics collection:', error.message);
    }
  }

  /**
   * Collect current system metrics
   */
  private async collectCurrentMetrics(): Promise<PerformanceMetrics> {
    const cpuUsage = await this.getCpuUsage();
    const memoryUsage = this.getMemoryUsage();
    const averageResponseTime = this.responseTimeCount > 0 ? this.responseTimeSum / this.responseTimeCount : 0;
    const errorRate = this.operationCount > 0 ? (this.errorCount / this.operationCount) * 100 : 0;
    const queueDepth = await this.getQueueDepth();
    
    return {
      timestamp: Date.now(),
      cpuUsage,
      memoryUsage,
      remindersSent: this.remindersSentCount,
      remindersScheduled: this.remindersScheduledCount,
      batchesProcessed: this.batchesProcessedCount,
      averageResponseTime,
      errorRate,
      queueDepth,
      systemLoad: this.calculateSystemLoad(cpuUsage, memoryUsage, errorRate),
    };
  }

  /**
   * Get CPU usage percentage
   */
  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startMeasure = this.cpuAverage();
      setTimeout(() => {
        const endMeasure = this.cpuAverage();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;
        const percentageCPU = 100 - ~~(100 * idleDifference / totalDifference);
        resolve(Math.max(0, Math.min(100, percentageCPU)));
      }, 100);
    });
  }

  /**
   * Calculate CPU average
   */
  private cpuAverage() {
    const cpus = os.cpus();
    let user = 0, nice = 0, sys = 0, idle = 0, irq = 0;
    
    for (const cpu of cpus) {
      user += cpu.times.user;
      nice += cpu.times.nice;
      sys += cpu.times.sys;
      idle += cpu.times.idle;
      irq += cpu.times.irq;
    }
    
    const total = user + nice + sys + idle + irq;
    return { idle, total };
  }

  /**
   * Get memory usage percentage
   */
  private getMemoryUsage(): number {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    return Math.round((usedMemory / totalMemory) * 100);
  }

  /**
   * Get queue depth from delayed job service
   */
  private async getQueueDepth(): Promise<number> {
    try {
      // Get delayed jobs count from Redis
      const queueKey = 'delayed_jobs_queue';
      const count = await this.redisClient.zCard(queueKey);
      return count || 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Calculate system load level
   */
  private calculateSystemLoad(cpuUsage: number, memoryUsage: number, errorRate: number): 'low' | 'medium' | 'high' | 'critical' {
    if (cpuUsage > 90 || memoryUsage > 90 || errorRate > 10) {
      return 'critical';
    } else if (cpuUsage > 70 || memoryUsage > 80 || errorRate > 5) {
      return 'high';
    } else if (cpuUsage > 50 || memoryUsage > 60 || errorRate > 2) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * Assess overall system health
   */
  private assessSystemHealth(metrics: PerformanceMetrics): SystemHealth {
    const cpuHealth = metrics.cpuUsage > 80 ? 'critical' : metrics.cpuUsage > 60 ? 'warning' : 'good';
    const memoryHealth = metrics.memoryUsage > 85 ? 'critical' : metrics.memoryUsage > 70 ? 'warning' : 'good';
    const performanceHealth = metrics.errorRate > 5 ? 'critical' : metrics.errorRate > 2 ? 'warning' : 'good';
    
    const criticalIssues = [cpuHealth, memoryHealth, performanceHealth].filter(h => h === 'critical').length;
    const warningIssues = [cpuHealth, memoryHealth, performanceHealth].filter(h => h === 'warning').length;
    
    let status: SystemHealth['status'];
    if (criticalIssues > 0) {
      status = 'critical';
    } else if (warningIssues > 1) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }
    
    const recommendations = this.generateRecommendations(metrics, cpuHealth, memoryHealth, performanceHealth);
    
    return {
      status,
      cpuHealth,
      memoryHealth,
      performanceHealth,
      recommendations,
    };
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(
    metrics: PerformanceMetrics,
    cpuHealth: string,
    memoryHealth: string,
    performanceHealth: string
  ): string[] {
    const recommendations: string[] = [];
    
    if (cpuHealth === 'critical') {
      recommendations.push('Reduce reminder scheduling frequency');
      recommendations.push('Consider scaling horizontally');
    } else if (cpuHealth === 'warning') {
      recommendations.push('Monitor CPU usage closely');
    }
    
    if (memoryHealth === 'critical') {
      recommendations.push('Clear cache and optimize memory usage');
      recommendations.push('Reduce batch sizes');
    } else if (memoryHealth === 'warning') {
      recommendations.push('Monitor memory usage');
    }
    
    if (performanceHealth === 'critical') {
      recommendations.push('Investigate error sources');
      recommendations.push('Reduce system load');
    }
    
    if (metrics.queueDepth > 1000) {
      recommendations.push('Queue depth is high - consider increasing processing capacity');
    }
    
    if (metrics.averageResponseTime > 5000) {
      recommendations.push('Response times are slow - optimize database queries');
    }
    
    return recommendations;
  }

  /**
   * Clean up old metrics data
   */
  private async cleanupOldMetrics() {
    try {
      const cutoffTime = Date.now() - (this.metricsRetentionHours * 60 * 60 * 1000);
      const allFields = await this.redisClient.hKeys(this.metricsKey);
      
      for (const field of allFields) {
        const timestamp = parseInt(field);
        if (timestamp < cutoffTime) {
          await this.redisClient.hDel(this.metricsKey, field);
        }
      }
    } catch (error) {
      console.error('❌ [PERFORMANCE-MONITOR] Error cleaning up metrics:', error.message);
    }
  }

  /**
   * Reset performance counters
   */
  private resetCounters() {
    this.remindersSentCount = 0;
    this.remindersScheduledCount = 0;
    this.batchesProcessedCount = 0;
    this.responseTimeSum = 0;
    this.responseTimeCount = 0;
    this.errorCount = 0;
    this.operationCount = 0;
  }

  // Public methods for tracking operations
  
  /**
   * Track reminder sent
   */
  trackReminderSent(count: number = 1) {
    this.remindersSentCount += count;
  }

  /**
   * Track reminder scheduled
   */
  trackReminderScheduled(count: number = 1) {
    this.remindersScheduledCount += count;
  }

  /**
   * Track batch processed
   */
  trackBatchProcessed() {
    this.batchesProcessedCount++;
  }

  /**
   * Track operation response time
   */
  trackResponseTime(timeMs: number) {
    this.responseTimeSum += timeMs;
    this.responseTimeCount++;
    this.operationCount++;
  }

  /**
   * Track error
   */
  trackError() {
    this.errorCount++;
    this.operationCount++;
  }

  /**
   * Get current system health
   */
  async getCurrentHealth(): Promise<SystemHealth | null> {
    try {
      const healthData = await this.redisClient.get(this.healthKey);
      return healthData ? JSON.parse(healthData) : null;
    } catch (error) {
      console.error('❌ [PERFORMANCE-MONITOR] Error getting health:', error.message);
      return null;
    }
  }

  /**
   * Get recent metrics
   */
  async getRecentMetrics(hours: number = 1): Promise<PerformanceMetrics[]> {
    try {
      const cutoffTime = Date.now() - (hours * 60 * 60 * 1000);
      const allFields = await this.redisClient.hKeys(this.metricsKey);
      const recentFields = allFields.filter(field => parseInt(field) >= cutoffTime);
      
      const metrics: PerformanceMetrics[] = [];
      for (const field of recentFields) {
        const data = await this.redisClient.hGet(this.metricsKey, field);
        if (data) {
          metrics.push(JSON.parse(data));
        }
      }
      
      return metrics.sort((a, b) => a.timestamp - b.timestamp);
    } catch (error) {
      console.error('❌ [PERFORMANCE-MONITOR] Error getting metrics:', error.message);
      return [];
    }
  }
}
