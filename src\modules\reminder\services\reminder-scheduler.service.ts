import { Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { ReminderService } from './reminder.service';

@Injectable()
export class ReminderSchedulerService {
  constructor(private readonly reminderService: ReminderService) {}

  /**
   * Cron job that runs every 15 minutes to check for reminders
   * Simple and efficient approach without complex scheduling
   */
  @Cron('0 */15 * * * *') // Every 15 minutes
  async checkReminders() {
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    console.log(`⏰ [REMINDER-CRON] Checking for reminders at ${currentTime} (15min interval)`);
    await this.reminderService.sendReminderNotifications();
  }
}
