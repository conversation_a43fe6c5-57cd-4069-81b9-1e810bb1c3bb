import { Injectable, OnModuleInit } from '@nestjs/common';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';
import { ReminderService } from './reminder.service';
import * as os from 'os';

interface LoadMetrics {
  cpuUsage: number;
  memoryUsage: number;
  timestamp: number;
}

@Injectable()
export class ReminderSchedulerService implements OnModuleInit {
  private currentCronInterval = 5; // minutes
  private loadHistory: LoadMetrics[] = [];
  private readonly maxLoadHistory = 12; // Keep 1 hour of 5-min intervals
  private readonly minInterval = 5; // minutes
  private readonly maxInterval = 60; // minutes
  private readonly offPeakStart = 0; // 12:00 AM
  private readonly offPeakEnd = 7.5; // 7:30 AM
  private readonly jobName = 'adaptive-reminder-check';

  constructor(
    private readonly reminderService: ReminderService,
    private readonly schedulerRegistry: SchedulerRegistry,
  ) {}

  onModuleInit() {
    this.scheduleAllExistingReminders();
    this.initializeAdaptiveScheduling();
  }

  /**
   * Load and schedule all active reminders on module initialization
   */
  async scheduleAllExistingReminders() {
    console.log('🔄 [REMINDER-SCHEDULER] Initializing reminder scheduling system...');
    await this.reminderService.scheduleAllActiveReminders();
  }

  /**
   * Initialize adaptive scheduling system
   */
  private initializeAdaptiveScheduling() {
    // Start with default 5-minute interval
    this.createAdaptiveCronJob(this.currentCronInterval);

    // Monitor load every minute to adjust scheduling
    const loadMonitorJob = new CronJob('0 * * * * *', () => {
      this.monitorSystemLoad();
    });

    this.schedulerRegistry.addCronJob('load-monitor', loadMonitorJob);
    loadMonitorJob.start();

    console.log('📊 [REMINDER-SCHEDULER] Adaptive scheduling initialized');
  }

  /**
   * Create or update the adaptive cron job
   */
  private createAdaptiveCronJob(intervalMinutes: number) {
    try {
      // Remove existing job if it exists
      if (this.schedulerRegistry.doesExist('cron', this.jobName)) {
        this.schedulerRegistry.deleteCronJob(this.jobName);
      }

      // Create new job with updated interval
      const cronExpression = `0 */${intervalMinutes} * * * *`;
      const job = new CronJob(cronExpression, () => {
        this.checkMissedReminders();
      });

      this.schedulerRegistry.addCronJob(this.jobName, job);
      job.start();

      console.log(`⚙️ [REMINDER-SCHEDULER] Updated cron interval to ${intervalMinutes} minutes`);
    } catch (error) {
      console.error('❌ [REMINDER-SCHEDULER] Failed to update cron job:', error.message);
    }
  }

  /**
   * Monitor system load and adjust scheduling frequency
   */
  private async monitorSystemLoad() {
    try {
      const cpuUsage = await this.getCpuUsage();
      const memoryUsage = this.getMemoryUsage();

      const metrics: LoadMetrics = {
        cpuUsage,
        memoryUsage,
        timestamp: Date.now(),
      };

      this.loadHistory.push(metrics);

      // Keep only recent history
      if (this.loadHistory.length > this.maxLoadHistory) {
        this.loadHistory.shift();
      }

      // Adjust scheduling frequency based on load and time
      this.adjustSchedulingFrequency();
    } catch (error) {
      console.error('❌ [LOAD-MONITOR] Error monitoring system load:', error.message);
    }
  }

  /**
   * Get CPU usage percentage
   */
  private getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startMeasure = this.cpuAverage();

      setTimeout(() => {
        const endMeasure = this.cpuAverage();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;
        const cpuPercentage = 100 - ~~(100 * idleDifference / totalDifference);
        resolve(cpuPercentage);
      }, 1000);
    });
  }

  /**
   * Calculate CPU average
   */
  private cpuAverage() {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    }

    return { idle: totalIdle / cpus.length, total: totalTick / cpus.length };
  }

  /**
   * Get memory usage percentage
   */
  private getMemoryUsage(): number {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    return ((totalMemory - freeMemory) / totalMemory) * 100;
  }

  /**
   * Adjust scheduling frequency based on load and time patterns
   */
  private adjustSchedulingFrequency() {
    if (this.loadHistory.length < 3) return; // Need some history

    const currentHour = new Date().getHours() + (new Date().getMinutes() / 60);
    const isOffPeak = currentHour >= this.offPeakStart && currentHour <= this.offPeakEnd;

    // Calculate average load over recent history
    const recentMetrics = this.loadHistory.slice(-3);
    const avgCpuUsage = recentMetrics.reduce((sum, m) => sum + m.cpuUsage, 0) / recentMetrics.length;
    const avgMemoryUsage = recentMetrics.reduce((sum, m) => sum + m.memoryUsage, 0) / recentMetrics.length;

    let newInterval = this.currentCronInterval;

    // Determine optimal interval based on load and time
    if (isOffPeak) {
      // During off-peak hours (12:00 AM - 7:30 AM), use longer intervals
      if (avgCpuUsage < 30 && avgMemoryUsage < 60) {
        newInterval = Math.min(this.maxInterval, this.currentCronInterval + 5);
      } else if (avgCpuUsage < 50 && avgMemoryUsage < 75) {
        newInterval = Math.min(30, this.currentCronInterval + 5);
      }
    } else {
      // During peak hours, adjust based on load
      if (avgCpuUsage > 80 || avgMemoryUsage > 85) {
        // High load: increase interval to reduce frequency
        newInterval = Math.min(this.maxInterval, this.currentCronInterval + 10);
      } else if (avgCpuUsage > 60 || avgMemoryUsage > 70) {
        // Medium load: slightly increase interval
        newInterval = Math.min(this.maxInterval, this.currentCronInterval + 5);
      } else if (avgCpuUsage < 30 && avgMemoryUsage < 50) {
        // Low load: can afford more frequent checks
        newInterval = Math.max(this.minInterval, this.currentCronInterval - 5);
      }
    }

    // Update interval if it changed significantly
    if (Math.abs(newInterval - this.currentCronInterval) >= 5) {
      this.currentCronInterval = newInterval;
      this.createAdaptiveCronJob(newInterval);

      console.log(`📈 [LOAD-ADAPTIVE] Load: CPU ${avgCpuUsage.toFixed(1)}%, Memory ${avgMemoryUsage.toFixed(1)}%, ` +
                  `Off-peak: ${isOffPeak}, New interval: ${newInterval}min`);
    }
  }

  /**
   * Adaptive reminder check with load-aware processing
   */
  async checkMissedReminders() {
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

    console.log(`⏰ [REMINDER-CRON] Adaptive check at ${currentTime} (${this.currentCronInterval}min interval)`);
    await this.reminderService.sendReminderNotifications();
  }
}
