import { Injectable } from '@nestjs/common';
import { register, Gauge, Counter } from 'prom-client';
import * as os from 'os';

export interface SystemLoad {
  cpuUsage: number;
  memoryUsage: number;
  queueDepth: number;
  activeConnections: number;
  timestamp: Date;
}

export interface LoadThresholds {
  cpu: { low: number; medium: number; high: number };
  memory: { low: number; medium: number; high: number };
  queue: { low: number; medium: number; high: number };
}

export enum LoadLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface SchedulingRecommendation {
  frequency: number; // in minutes
  batchSize: number;
  loadLevel: LoadLevel;
  reason: string;
  isOffPeak: boolean;
}

@Injectable()
export class LoadMonitorService {
  private readonly loadHistory: SystemLoad[] = [];
  private readonly maxHistorySize = 100; // Keep last 100 readings
  
  // Prometheus metrics
  private readonly cpuGauge = new Gauge({
    name: 'reminder_system_cpu_usage',
    help: 'CPU usage percentage for reminder system monitoring',
    registers: [register]
  });
  
  private readonly memoryGauge = new Gauge({
    name: 'reminder_system_memory_usage',
    help: 'Memory usage percentage for reminder system monitoring',
    registers: [register]
  });
  
  private readonly queueDepthGauge = new Gauge({
    name: 'reminder_system_queue_depth',
    help: 'Current queue depth for reminder processing',
    registers: [register]
  });
  
  private readonly schedulingFrequencyGauge = new Gauge({
    name: 'reminder_system_scheduling_frequency',
    help: 'Current scheduling frequency in minutes',
    registers: [register]
  });
  
  private readonly loadLevelCounter = new Counter({
    name: 'reminder_system_load_level_total',
    help: 'Total count of different load levels detected',
    labelNames: ['level'],
    registers: [register]
  });

  // Default thresholds - can be configured based on server specs
  private readonly defaultThresholds: LoadThresholds = {
    cpu: { low: 30, medium: 60, high: 85 },
    memory: { low: 40, medium: 70, high: 90 },
    queue: { low: 10, medium: 50, high: 100 }
  };

  // Off-peak hours: 12:00 AM to 7:30 AM (server time)
  private readonly offPeakStart = 0; // 12:00 AM
  private readonly offPeakEnd = 7.5; // 7:30 AM

  /**
   * Get current system load metrics
   */
  async getCurrentLoad(): Promise<SystemLoad> {
    const cpuUsage = await this.getCpuUsage();
    const memoryUsage = this.getMemoryUsage();
    const queueDepth = await this.getQueueDepth();
    const activeConnections = await this.getActiveConnections();

    const load: SystemLoad = {
      cpuUsage,
      memoryUsage,
      queueDepth,
      activeConnections,
      timestamp: new Date()
    };

    // Update Prometheus metrics
    this.cpuGauge.set(cpuUsage);
    this.memoryGauge.set(memoryUsage);
    this.queueDepthGauge.set(queueDepth);

    // Store in history
    this.addToHistory(load);

    return load;
  }

  /**
   * Get CPU usage percentage
   */
  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startMeasure = this.cpuAverage();
      
      setTimeout(() => {
        const endMeasure = this.cpuAverage();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;
        const cpuPercentage = 100 - Math.round(100 * idleDifference / totalDifference);
        resolve(Math.max(0, Math.min(100, cpuPercentage)));
      }, 100);
    });
  }

  /**
   * Helper function to calculate CPU average
   */
  private cpuAverage(): { idle: number; total: number } {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    }

    return {
      idle: totalIdle / cpus.length,
      total: totalTick / cpus.length
    };
  }

  /**
   * Get memory usage percentage
   */
  private getMemoryUsage(): number {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    return Math.round((usedMemory / totalMemory) * 100);
  }

  /**
   * Get current queue depth (simplified - would need actual queue monitoring)
   */
  private async getQueueDepth(): Promise<number> {
    // This is a placeholder - in a real implementation, you'd query your queue system
    // For now, we'll simulate based on time of day and current load
    const now = new Date();
    const hour = now.getHours();
    
    // Simulate higher queue depth during peak hours
    if (hour >= 8 && hour <= 22) {
      return Math.floor(Math.random() * 20) + 5; // 5-25 during peak
    } else {
      return Math.floor(Math.random() * 5); // 0-5 during off-peak
    }
  }

  /**
   * Get active connections count (simplified)
   */
  private async getActiveConnections(): Promise<number> {
    // Placeholder - would integrate with actual connection monitoring
    return Math.floor(Math.random() * 100) + 10;
  }

  /**
   * Add load measurement to history
   */
  private addToHistory(load: SystemLoad): void {
    this.loadHistory.push(load);
    if (this.loadHistory.length > this.maxHistorySize) {
      this.loadHistory.shift();
    }
  }

  /**
   * Determine current load level
   */
  determineLoadLevel(load: SystemLoad, thresholds: LoadThresholds = this.defaultThresholds): LoadLevel {
    const { cpuUsage, memoryUsage, queueDepth } = load;
    
    // Critical level - any metric is at critical threshold
    if (cpuUsage >= 95 || memoryUsage >= 95 || queueDepth >= 200) {
      this.loadLevelCounter.inc({ level: LoadLevel.CRITICAL });
      return LoadLevel.CRITICAL;
    }
    
    // High level - any metric exceeds high threshold
    if (cpuUsage >= thresholds.cpu.high || 
        memoryUsage >= thresholds.memory.high || 
        queueDepth >= thresholds.queue.high) {
      this.loadLevelCounter.inc({ level: LoadLevel.HIGH });
      return LoadLevel.HIGH;
    }
    
    // Medium level - any metric exceeds medium threshold
    if (cpuUsage >= thresholds.cpu.medium || 
        memoryUsage >= thresholds.memory.medium || 
        queueDepth >= thresholds.queue.medium) {
      this.loadLevelCounter.inc({ level: LoadLevel.MEDIUM });
      return LoadLevel.MEDIUM;
    }
    
    // Low level - all metrics are below medium threshold
    this.loadLevelCounter.inc({ level: LoadLevel.LOW });
    return LoadLevel.LOW;
  }

  /**
   * Check if current time is during off-peak hours
   */
  isOffPeakTime(): boolean {
    const now = new Date();
    const currentHour = now.getHours() + (now.getMinutes() / 60);
    
    return currentHour >= this.offPeakStart && currentHour <= this.offPeakEnd;
  }

  /**
   * Get scheduling recommendation based on current load and time
   */
  async getSchedulingRecommendation(): Promise<SchedulingRecommendation> {
    const currentLoad = await this.getCurrentLoad();
    const loadLevel = this.determineLoadLevel(currentLoad);
    const isOffPeak = this.isOffPeakTime();
    
    let frequency: number;
    let batchSize: number;
    let reason: string;
    
    // Base recommendations on load level and peak/off-peak status
    switch (loadLevel) {
      case LoadLevel.CRITICAL:
        frequency = isOffPeak ? 30 : 60; // Reduce frequency significantly
        batchSize = 5;
        reason = 'Critical load detected - minimizing processing frequency';
        break;
        
      case LoadLevel.HIGH:
        frequency = isOffPeak ? 15 : 30;
        batchSize = 10;
        reason = 'High load detected - reducing processing frequency';
        break;
        
      case LoadLevel.MEDIUM:
        frequency = isOffPeak ? 10 : 15;
        batchSize = 20;
        reason = 'Medium load detected - moderate processing frequency';
        break;
        
      case LoadLevel.LOW:
        frequency = isOffPeak ? 5 : 10;
        batchSize = 50;
        reason = 'Low load detected - optimal processing frequency';
        break;
        
      default:
        frequency = 10;
        batchSize = 20;
        reason = 'Default scheduling parameters';
    }
    
    // Apply off-peak optimizations
    if (isOffPeak) {
      batchSize = Math.floor(batchSize * 1.5); // Increase batch size during off-peak
      reason += ' (off-peak optimization applied)';
    }
    
    // Update metrics
    this.schedulingFrequencyGauge.set(frequency);
    
    return {
      frequency,
      batchSize,
      loadLevel,
      reason,
      isOffPeak
    };
  }

  /**
   * Get load statistics for the last N measurements
   */
  getLoadStatistics(count: number = 10): {
    averageCpu: number;
    averageMemory: number;
    averageQueue: number;
    peakCpu: number;
    peakMemory: number;
    peakQueue: number;
  } {
    const recentLoads = this.loadHistory.slice(-count);

    if (recentLoads.length === 0) {
      return {
        averageCpu: 0,
        averageMemory: 0,
        averageQueue: 0,
        peakCpu: 0,
        peakMemory: 0,
        peakQueue: 0
      };
    }

    const averageCpu = recentLoads.reduce((sum, load) => sum + load.cpuUsage, 0) / recentLoads.length;
    const averageMemory = recentLoads.reduce((sum, load) => sum + load.memoryUsage, 0) / recentLoads.length;
    const averageQueue = recentLoads.reduce((sum, load) => sum + load.queueDepth, 0) / recentLoads.length;

    const peakCpu = Math.max(...recentLoads.map(load => load.cpuUsage));
    const peakMemory = Math.max(...recentLoads.map(load => load.memoryUsage));
    const peakQueue = Math.max(...recentLoads.map(load => load.queueDepth));

    return {
      averageCpu: Math.round(averageCpu * 10) / 10,
      averageMemory: Math.round(averageMemory * 10) / 10,
      averageQueue: Math.round(averageQueue * 10) / 10,
      peakCpu,
      peakMemory,
      peakQueue
    };
  }

  /**
   * Health check for the load monitoring system
   */
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; message: string; load?: SystemLoad }> {
    try {
      const currentLoad = await this.getCurrentLoad();
      const loadLevel = this.determineLoadLevel(currentLoad);

      if (loadLevel === LoadLevel.CRITICAL) {
        return {
          status: 'unhealthy',
          message: `System under critical load - CPU: ${currentLoad.cpuUsage}%, Memory: ${currentLoad.memoryUsage}%, Queue: ${currentLoad.queueDepth}`,
          load: currentLoad
        };
      }

      return {
        status: 'healthy',
        message: `Load monitoring operational - Current level: ${loadLevel}`,
        load: currentLoad
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Load monitoring failed: ${error.message}`
      };
    }
  }
}
