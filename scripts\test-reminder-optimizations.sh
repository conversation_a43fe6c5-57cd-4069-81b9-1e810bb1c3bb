#!/bin/bash

# Test script for reminder optimization features
# This script runs comprehensive tests for all optimization components

echo "🧪 Starting Reminder Optimization Test Suite..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
    esac
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_status "ERROR" "Please run this script from the project root directory"
    exit 1
fi

print_status "INFO" "Checking test environment..."

# Check if Jest is available
if ! command -v npx &> /dev/null; then
    print_status "ERROR" "npx is not available. Please install Node.js and npm."
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "INFO" "Installing dependencies..."
    yarn install
fi

print_status "INFO" "Running reminder optimization tests..."

# Run the specific test file
echo ""
echo "🔬 Testing Intelligent Batching Strategy..."
npx jest src/modules/reminder/tests/reminder-optimization.test.ts --testNamePattern="Intelligent Batching Strategy" --verbose

echo ""
echo "🔬 Testing Redis-based Delayed Job System..."
npx jest src/modules/reminder/tests/reminder-optimization.test.ts --testNamePattern="Redis-based Delayed Job System" --verbose

echo ""
echo "🔬 Testing Adaptive Scheduling System..."
npx jest src/modules/reminder/tests/reminder-optimization.test.ts --testNamePattern="Adaptive Scheduling System" --verbose

echo ""
echo "🔬 Testing Performance Monitoring..."
npx jest src/modules/reminder/tests/reminder-optimization.test.ts --testNamePattern="Performance Monitoring" --verbose

echo ""
echo "🔬 Testing Database Query Optimization..."
npx jest src/modules/reminder/tests/reminder-optimization.test.ts --testNamePattern="Database Query Optimization" --verbose

echo ""
echo "🔬 Running Performance Benchmarks..."
npx jest src/modules/reminder/tests/reminder-optimization.test.ts --testNamePattern="Performance Benchmarks" --verbose

echo ""
echo "🔬 Running End-to-End Integration Tests..."
npx jest src/modules/reminder/tests/reminder-optimization.test.ts --testNamePattern="End-to-End Integration" --verbose

# Run all tests together for final verification
echo ""
echo "🔬 Running Complete Test Suite..."
TEST_RESULT=$(npx jest src/modules/reminder/tests/reminder-optimization.test.ts --silent)
TEST_EXIT_CODE=$?

if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_status "SUCCESS" "All reminder optimization tests passed!"
    echo ""
    echo "📊 Test Summary:"
    echo "✅ Intelligent Batching Strategy - Optimized"
    echo "✅ Redis-based Delayed Jobs - Reliable"
    echo "✅ Adaptive Scheduling - Load-aware"
    echo "✅ Performance Monitoring - Comprehensive"
    echo "✅ Database Optimization - Efficient"
    echo "✅ End-to-End Integration - Working"
    echo ""
    print_status "SUCCESS" "Reminder system is optimized and ready for production!"
else
    print_status "ERROR" "Some tests failed. Please review the output above."
    echo ""
    print_status "WARNING" "Common issues to check:"
    echo "  - Redis connection configuration"
    echo "  - Database connection and indexes"
    echo "  - Queue system configuration"
    echo "  - Environment variables"
    exit 1
fi

echo ""
echo "🚀 Next Steps:"
echo "1. Deploy the optimized reminder system"
echo "2. Monitor performance metrics in production"
echo "3. Adjust scheduling parameters based on real load"
echo "4. Set up alerts for system health monitoring"
echo ""
print_status "INFO" "Optimization test suite completed successfully!"
