import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { Reminder } from 'src/entity/reminder';

const ReminderSchema = new Schema<Reminder>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      required: true,
      index: true, // Admin ID who created the reminder
    },
    title: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    time: {
      type: String, // Time in 24-hour format (HH:MM)
      required: true,
      index: true, // Index for time-based queries
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true, // Index for active status filtering
    },
    isRepeating: {
      type: Boolean,
      default: false,
      index: true, // Index for repeating status filtering
    },
    repeatDays: {
      type: [String], // Array of days ['monday', 'tuesday', etc.]
      default: [],
      index: true, // Index for day-based filtering
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Compound indexes for optimized queries
ReminderSchema.index({ time: 1, isActive: 1 }); // Primary query pattern
ReminderSchema.index({ isActive: 1, isRepeating: 1, repeatDays: 1 }); // Repeating reminders
ReminderSchema.index({ userId: 1, isActive: 1 }); // User-specific active reminders
ReminderSchema.index({ time: 1, isActive: 1, isRepeating: 1 }); // Time-based active reminders

const ReminderModel = model<Reminder>('reminder', ReminderSchema);
export { ReminderModel };
