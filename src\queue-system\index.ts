/* eslint-disable @typescript-eslint/ban-types */
import * as amqp from 'amqplib';
import { notificationConfig } from 'config/notification';
import { QueueConsumeHandler } from './consume-handler';
const amqpUrl = notificationConfig.amqp_url;

/**
 * @var {Promise<Queue>}
 */
let instance: Promise<Queue>;

/**
 * queue for async messaging
 */
class Queue {
  connection: amqp.Connection;
  channel: any;
  static getInstance: () => Promise<any>;
  /**
   * Initialize connection to rabbitMQ
   */
  async init() {
    try {
      this.connection = await amqp.connect(amqpUrl);
      this.channel = await this.connection.createChannel();
      return this;
    } catch (error: any) {
      console.error('Failed to connect to RabbitMQ:', error.message);
      throw error;
    }
  }

  /**
   * Send payload to queue
   * @param {String} queue Queue name
   * @param {Buffer} payload Payload as Buffer
   * @param {Object} options Optional parameters including delay
   */
  async sendPayload(queue: string, payload: Buffer, options?: { delay?: number }) {
    try {
      if (!this.connection) {
        await this.init();
      }
      await this.channel.assertQueue(queue, { durable: false });

      if (options?.delay && options.delay > 0) {
        // Use Redis-based delayed job service for reliable delayed execution
        const { DelayedJobService } = await import('../modules/reminder/services/delayed-job.service');
        const delayedJobService = new DelayedJobService();
        await delayedJobService.onModuleInit();

        const jobId = `delayed_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const payloadData = JSON.parse(payload.toString());

        const success = await delayedJobService.scheduleJob(
          jobId,
          payloadData,
          queue,
          options.delay
        );

        if (!success) {
          console.warn(`⚠️ [QUEUE] Failed to schedule delayed job, falling back to immediate send`);
          await this.channel.sendToQueue(queue, payload);
        }
      } else {
        await this.channel.sendToQueue(queue, payload);
      }
    } catch (error: any) {
      console.error(`Failed to send payload to queue ${queue}:`, error.message);
      throw error;
    }
  }

  /**
   * @param {String} queue Queue name
   * @returns {void}
   */
  async consume(queue: string): Promise<void> {
    try {
      if (!this.connection) {
        await this.init();
      }
      await this.channel.assertQueue(queue, { durable: false });
      this.channel.consume(
        queue,
        async function (msg: { content: { toString: () => string } }) {
          const payload = await JSON.parse(msg.content.toString());
          await QueueConsumeHandler.handle(payload);
        },
        {
          noAck: true,
        },
      );
    } catch (error: any) {
      console.log(error.message);
    }
  }

  async close() {
    try {
      if (this.channel) {
        await this.channel.close();
        await this.connection.close();
      }
    } catch (error: any) {
      console.log(error.message);
    }
  }
}

/**
 * @return {Promise<Queue>}
 */
Queue.getInstance = async function (): Promise<Queue> {
  try {
    if (!instance) {
      const broker = new Queue();
      instance = broker.init();
    }
    const result = await instance;
    if (!result) {
      throw new Error('Failed to initialize queue connection');
    }
    return result;
  } catch (error: any) {
    console.error('Queue getInstance error:', error.message);
    throw error;
  }
};

export const QueueInstance = Queue.getInstance();
