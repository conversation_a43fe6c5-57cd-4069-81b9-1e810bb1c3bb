import { Injectable } from '@nestjs/common';
import { FcmService } from 'src/helper/fcmService';

@Injectable()
export class TopicManagementService {
  constructor(private readonly fcmService: FcmService) {}

  /**
   * Subscribe user token to the all-reminders topic
   */
  async subscribeUserToReminders(fcmToken: string): Promise<boolean> {
    try {
      if (!fcmToken || fcmToken.trim() === '') {
        console.warn('Empty FCM token provided for subscription');
        return false;
      }

      await this.fcmService.subscribeNotificationTopic(fcmToken, 'all-reminders');
      console.log(`✅ [TOPIC-MGMT] Subscribed user to reminders topic`);
      return true;
    } catch (error) {
      console.error('❌ [TOPIC-MGMT] Failed to subscribe to reminders topic:', error.message);
      return false;
    }
  }

  /**
   * Unsubscribe user token from the all-reminders topic
   */
  async unsubscribeUserFromReminders(fcmToken: string): Promise<boolean> {
    try {
      if (!fcmToken || fcmToken.trim() === '') {
        console.warn('Empty FCM token provided for unsubscription');
        return false;
      }

      await this.fcmService.unsubscribeNotificationTopic(fcmToken, 'all-reminders');
      console.log(`✅ [TOPIC-MGMT] Unsubscribed user from reminders topic`);
      return true;
    } catch (error) {
      console.error('❌ [TOPIC-MGMT] Failed to unsubscribe from reminders topic:', error.message);
      return false;
    }
  }
}
