import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON>minderController } from './rest/reminder.controller';
import { ReminderService } from './services/reminder.service';
import { ReminderRepository } from './repositories/reminder.repository';
import { ReminderSchedulerService } from './services/reminder-scheduler.service';
import { DelayedJobService } from './services/delayed-job.service';
import { PerformanceMonitorService } from './services/performance-monitor.service';
import { TopicManagementService } from './services/topic-management.service';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [NotificationModule],
  controllers: [ReminderController],
  providers: [
    ReminderService,
    ReminderRepository,
    ReminderSchedulerService,
    DelayedJobService,
    PerformanceMonitorService,
    TopicManagementService,
  ],
  exports: [ReminderService, DelayedJobService, PerformanceMonitorService, TopicManagementService],
})
export class ReminderModule {}
