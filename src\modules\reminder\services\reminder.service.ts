import { Injectable } from '@nestjs/common';
import { ReminderRepository } from '../repositories/reminder.repository';
import { CreateReminderDto, UpdateReminderDto } from '../rest/dto/reminder.dto';
import { Reminder } from 'src/entity/reminder';
import { NotificationStatsService } from '../../notification/services/notification-stats.service';
import { QueueInstance } from 'src/queue-system';
import {
  NotificationQueueName,
  QueuePayloadType,
} from 'src/queue-system/predefined-data';

@Injectable()
export class ReminderService {
  constructor(
    private readonly reminderRepository: ReminderRepository,
    private readonly notificationStatsService: NotificationStatsService,
  ) {}

  async createReminder(
    adminId: string,
    createReminderDto: CreateReminderDto,
  ): Promise<Reminder> {
    const reminder = await this.reminderRepository.createReminder(adminId, createReminderDto);
    console.log(`✅ [REMINDER-CREATE] Created reminder "${reminder.title}" for ${reminder.time}`);
    return reminder;
  }

  async getAllReminders(): Promise<Reminder[]> {
    return this.reminderRepository.findAllReminders();
  }

  async getReminderById(id: string): Promise<Reminder> {
    return this.reminderRepository.findReminderById(id);
  }

  async updateReminder(
    id: string,
    updateReminderDto: UpdateReminderDto,
  ): Promise<Reminder> {
    const oldReminder = await this.reminderRepository.findReminderById(id);
    const reminder = await this.reminderRepository.updateReminder(id, updateReminderDto);
    console.log(`📝 [REMINDER-UPDATE] "${reminder.title}" time changed: ${oldReminder.time} → ${reminder.time}`);
    return reminder;
  }

  async deleteReminder(id: string): Promise<boolean> {
    return this.reminderRepository.deleteReminder(id);
  }

  async sendReminderNotifications(): Promise<void> {
    try {
      // Get current time in HH:MM format
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now
        .getMinutes()
        .toString()
        .padStart(2, '0')}`;

      // Find all active reminders for the current time
      const activeReminders = await this.reminderRepository.findActiveRemindersForTime(currentTime);

      if (activeReminders.length === 0) {
        console.log(`⏰ [REMINDER-TRIGGER] No reminders found for ${currentTime}`);
        return;
      }

      console.log(`🎯 [REMINDER-TRIGGER] Processing ${activeReminders.length} reminder(s) for ${currentTime}`);

      // Start batch tracking
      const batchId = this.notificationStatsService.generateBatchId('reminder-batch');
      this.notificationStatsService.startBatch(
        batchId,
        activeReminders.length,
        activeReminders.map(r => r.title).join(', '),
        'topic'
      );

      // Send notifications for all reminders
      await this.sendNotifications(activeReminders, batchId);

    } catch (error) {
      console.error('❌ [REMINDER-TRIGGER] Error sending reminder notifications:', error.message);
    }
  }

  /**
   * Send notifications for reminders to the queue
   */
  private async sendNotifications(reminders: Reminder[], batchId: string): Promise<void> {
    const queueInstance = await QueueInstance;

    for (const reminder of reminders) {
      try {
        const payload = {
          title: reminder.title,
          body: reminder.message,
          documentId: reminder.id,
          data: {
            reminderId: reminder.id,
            localTime: reminder.time,
          },
          topic: 'all-reminders',
          task: 'topic-sender',
          payloadType: QueuePayloadType.REMINDER_TOPIC_SENDER,
          isHighPriority: true,
        };

        await queueInstance.sendPayload(
          NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE,
          Buffer.from(JSON.stringify(payload)),
        );

        this.notificationStatsService.incrementSent(batchId);
        console.log(`📤 [REMINDER-QUEUE] Queued "${reminder.title}" → topic 'all-reminders'`);
      } catch (error) {
        this.notificationStatsService.incrementFailed(batchId);
        console.error(`❌ [REMINDER-QUEUE] Failed to queue "${reminder.title}":`, error.message);
      }
    }
  }



}
