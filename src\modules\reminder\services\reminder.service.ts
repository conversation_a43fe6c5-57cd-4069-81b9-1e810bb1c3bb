import { Injectable } from '@nestjs/common';
import { ReminderRepository } from '../repositories/reminder.repository';
import { CreateReminderDto, UpdateReminderDto } from '../rest/dto/reminder.dto';
import { Reminder } from 'src/entity/reminder';
import { NotificationStatsService } from '../../notification/services/notification-stats.service';
import { QueueInstance } from 'src/queue-system';
import {
  NotificationQueueName,
  QueuePayloadType,
} from 'src/queue-system/predefined-data';
import { DelayedJobService } from './delayed-job.service';
import { PerformanceMonitorService } from './performance-monitor.service';

@Injectable()
export class ReminderService {
  private readonly delayedJobService: DelayedJobService;
  private readonly performanceMonitor: PerformanceMonitorService;

  constructor(
    private readonly reminderRepository: ReminderRepository,
    private readonly notificationStatsService: NotificationStatsService,
  ) {
    this.delayedJobService = new DelayedJobService();
    this.delayedJobService.onModuleInit();

    this.performanceMonitor = new PerformanceMonitorService();
    this.performanceMonitor.onModuleInit();
  }

  async createReminder(
    adminId: string,
    createReminderDto: CreateReminderDto,
  ): Promise<Reminder> {
    const reminder = await this.reminderRepository.createReminder(adminId, createReminderDto);
    if (reminder.isActive) {
      await this.scheduleReminder(reminder);
    }
    return reminder;
  }

  async getAllReminders(): Promise<Reminder[]> {
    return this.reminderRepository.findAllReminders();
  }

  async getReminderById(id: string): Promise<Reminder> {
    return this.reminderRepository.findReminderById(id);
  }

  async updateReminder(
    id: string,
    updateReminderDto: UpdateReminderDto,
  ): Promise<Reminder> {
    const oldReminder = await this.reminderRepository.findReminderById(id);
    const reminder = await this.reminderRepository.updateReminder(id, updateReminderDto);
    console.log(`📝 [REMINDER-UPDATE] "${reminder.title}" time changed: ${oldReminder.time} → ${reminder.time}`);
    if (reminder.isActive) {
      await this.scheduleReminder(reminder);
    }
    return reminder;
  }

  async deleteReminder(id: string): Promise<boolean> {
    return this.reminderRepository.deleteReminder(id);
  }

  async sendReminderNotifications(): Promise<void> {
    const startTime = Date.now();

    try {
      const currentTime = this.getCurrentTimeString();

      // Use intelligent batching with lookahead for better performance
      const reminderBatch = await this.getIntelligentReminderBatch(currentTime);

      if (reminderBatch.totalReminders === 0) {
        console.log(`⏰ [REMINDER-TRIGGER] No reminders found for ${currentTime}`);
        this.performanceMonitor.trackResponseTime(Date.now() - startTime);
        return;
      }

      console.log(`🎯 [REMINDER-TRIGGER] Processing ${reminderBatch.totalReminders} reminder(s) for ${currentTime} ` +
                  `(${reminderBatch.currentTimeReminders.length} current, ${reminderBatch.lookaheadReminders.length} lookahead)`);

      const batchId = this.notificationStatsService.generateBatchId('intelligent-reminder-batch');
      this.notificationStatsService.startBatch(
        batchId,
        reminderBatch.totalReminders,
        reminderBatch.allReminders.map(r => r.title).join(', '),
        'topic'
      );

      // Process current time reminders immediately
      if (reminderBatch.currentTimeReminders.length > 0) {
        const currentGroups = this.groupRemindersByIntelligentStrategy(reminderBatch.currentTimeReminders);
        await this.sendBatchedNotifications(currentGroups, batchId);
        this.performanceMonitor.trackBatchProcessed();
      }

      // Schedule lookahead reminders for precise delivery
      if (reminderBatch.lookaheadReminders.length > 0) {
        await this.scheduleLookaheadReminders(reminderBatch.lookaheadReminders);
      }

      // Track successful operation
      this.performanceMonitor.trackResponseTime(Date.now() - startTime);
    } catch (error) {
      this.performanceMonitor.trackError();
      this.performanceMonitor.trackResponseTime(Date.now() - startTime);
      console.error('❌ [REMINDER-TRIGGER] Error sending reminder notifications:', error.message);
    }
  }

  /**
   * Send batched notifications for grouped reminders
   */
  private async sendBatchedNotifications(groupedReminders: Reminder[][], batchId: string): Promise<void> {
    const queueInstance = await QueueInstance;

    for (const group of groupedReminders) {
      const groupStartTime = Date.now();

      try {
        const payload = this.createNotificationPayload(group);

        await queueInstance.sendPayload(
          NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE,
          Buffer.from(JSON.stringify(payload)),
        );

        // Track successful sends
        group.forEach(() => {
          this.notificationStatsService.incrementSent(batchId);
          this.performanceMonitor.trackReminderSent();
        });

        // Track response time for this group
        this.performanceMonitor.trackResponseTime(Date.now() - groupStartTime);

        // Log success
        this.logNotificationSuccess(group);
      } catch (error) {
        // Track failed sends
        group.forEach(() => {
          this.notificationStatsService.incrementFailed(batchId);
          this.performanceMonitor.trackError();
        });

        console.error(`❌ [REMINDER-QUEUE] Failed to queue reminder(s):`,
          group.map((r: Reminder) => `"${r.title}"`).join(', '), '- Error:', error.message);
      }
    }
  }

  /**
   * Create notification payload for a group of reminders
   */
  private createNotificationPayload(group: Reminder[]) {
    let title = group[0].title;
    let body = group[0].message;

    const data = {
      reminderId: group[0].id,
      localTime: group[0].time,
      groupedIds: group.map((r: Reminder) => r.id).join(','),
    };

    if (group.length > 1) {
      title = "Multiple Reminders";
      body = group.map((r: Reminder) => `${r.title} at ${r.time}`).join('\n');
    }

    return {
      title,
      body,
      documentId: group[0].id,
      data,
      topic: 'all-reminders',
      task: 'topic-sender',
      payloadType: QueuePayloadType.REMINDER_TOPIC_SENDER,
      isHighPriority: true,
    };
  }

  /**
   * Log notification success message
   */
  private logNotificationSuccess(group: Reminder[]): void {
    if (group.length === 1) {
      console.log(`📤 [REMINDER-QUEUE] Queued for broadcast: "${group[0].title}" → topic 'all-reminders'`);
    } else {
      const titles = group.map((r: Reminder) => `"${r.title}"`).join(', ');
      console.log(`📤 [REMINDER-QUEUE] Queued for broadcast: ${group.length} reminders → topic 'all-reminders' (${titles})`);
    }
  }

  /**
   * Get intelligent reminder batch with lookahead processing
   */
  private async getIntelligentReminderBatch(currentTime: string): Promise<{
    currentTimeReminders: Reminder[];
    lookaheadReminders: Reminder[];
    allReminders: Reminder[];
    totalReminders: number;
  }> {
    // Generate lookahead times (next 15 minutes in 5-minute intervals)
    const lookaheadTimes = this.generateLookaheadTimes(currentTime, 3, 5);
    const allTimes = [currentTime, ...lookaheadTimes];

    // Batch query for all times
    const remindersByTime = await this.reminderRepository.findActiveRemindersForTimes(allTimes);

    const currentTimeReminders = remindersByTime.get(currentTime) || [];
    const lookaheadReminders: Reminder[] = [];

    // Collect lookahead reminders
    for (const time of lookaheadTimes) {
      const timeReminders = remindersByTime.get(time) || [];
      lookaheadReminders.push(...timeReminders);
    }

    const allReminders = [...currentTimeReminders, ...lookaheadReminders];

    return {
      currentTimeReminders,
      lookaheadReminders,
      allReminders,
      totalReminders: allReminders.length,
    };
  }

  /**
   * Generate lookahead times for batch processing
   */
  private generateLookaheadTimes(currentTime: string, count: number, intervalMinutes: number): string[] {
    const [hours, minutes] = currentTime.split(':').map(Number);
    const times: string[] = [];

    for (let i = 1; i <= count; i++) {
      const totalMinutes = hours * 60 + minutes + (i * intervalMinutes);
      const newHours = Math.floor(totalMinutes / 60) % 24;
      const newMinutes = totalMinutes % 60;

      const timeString = `${newHours.toString().padStart(2, '0')}:${newMinutes.toString().padStart(2, '0')}`;
      times.push(timeString);
    }

    return times;
  }

  /**
   * Group reminders using intelligent strategy based on content similarity and timing
   */
  private groupRemindersByIntelligentStrategy(reminders: Reminder[]): Reminder[][] {
    if (reminders.length === 0) return [];

    // Sort by time first
    const sortedReminders = reminders.sort((a, b) => {
      const timeA = this.parseTimeToMs(a.time);
      const timeB = this.parseTimeToMs(b.time);
      return timeA - timeB;
    });

    const groups: Reminder[][] = [];
    const processed = new Set<string>();

    for (const reminder of sortedReminders) {
      if (processed.has(reminder.id)) continue;

      const group = [reminder];
      processed.add(reminder.id);

      // Find similar reminders to group together
      for (const other of sortedReminders) {
        if (processed.has(other.id)) continue;

        if (this.shouldGroupReminders(reminder, other)) {
          group.push(other);
          processed.add(other.id);

          // Limit group size for optimal performance
          if (group.length >= 5) break;
        }
      }

      groups.push(group);
    }

    return groups;
  }

  /**
   * Determine if two reminders should be grouped together
   */
  private shouldGroupReminders(reminder1: Reminder, reminder2: Reminder): boolean {
    // Group by time proximity (within 5 minutes)
    const time1Ms = this.parseTimeToMs(reminder1.time);
    const time2Ms = this.parseTimeToMs(reminder2.time);
    const timeDiff = Math.abs(time1Ms - time2Ms);

    if (timeDiff <= 5 * 60 * 1000) { // 5 minutes
      return true;
    }

    // Group by content similarity (same title or similar message)
    if (reminder1.title === reminder2.title) {
      return true;
    }

    // Group by user (same creator)
    if (reminder1.userId === reminder2.userId && timeDiff <= 15 * 60 * 1000) { // 15 minutes for same user
      return true;
    }

    return false;
  }

  /**
   * Schedule lookahead reminders for precise delivery
   */
  private async scheduleLookaheadReminders(reminders: Reminder[]): Promise<void> {
    const groupedReminders = this.groupRemindersByIntelligentStrategy(reminders);

    for (const group of groupedReminders) {
      const representativeReminder = group[0];
      const delayMs = this.calculateReminderDelay(representativeReminder.time);

      // Only schedule if delay is reasonable (not too far in the future)
      if (delayMs <= 60 * 60 * 1000) { // Max 1 hour
        const payload = this.createNotificationPayload(group);
        const jobId = `lookahead_${representativeReminder.id}_${Date.now()}`;

        await this.delayedJobService.scheduleJob(
          jobId,
          payload,
          NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE,
          delayMs
        );

        console.log(`🔮 [LOOKAHEAD] Scheduled ${group.length} reminder(s) for ${representativeReminder.time}`);
      }
    }
  }

  /**
   * Get current time in HH:MM format
   */
  private getCurrentTimeString(): string {
    const now = new Date();
    return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
  }



  /**
   * Parse HH:MM time string to milliseconds for comparison
   */
  private parseTimeToMs(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return (hours * 60 * 60 * 1000) + (minutes * 60 * 1000);
  }

  /**
   * Schedule all active reminders on system startup or module initialization
   */
  async scheduleAllActiveReminders(): Promise<void> {
    try {
      const activeReminders = await this.reminderRepository.findAllActiveReminders();
      console.log(`📅 [REMINDER-SCHEDULER] Found ${activeReminders.length} active reminder(s) to schedule`);

      const scheduledCount = await this.scheduleRemindersInBatch(activeReminders);

      if (scheduledCount > 0) {
        console.log(`✅ [REMINDER-SCHEDULER] Successfully scheduled ${scheduledCount} reminder(s)`);
      }
    } catch (error) {
      console.error('❌ [REMINDER-SCHEDULER] Error scheduling active reminders:', error.message);
    }
  }

  /**
   * Schedule multiple reminders in batch for better performance
   */
  private async scheduleRemindersInBatch(reminders: Reminder[]): Promise<number> {
    let scheduledCount = 0;
    const batchSize = 10; // Process in batches to avoid overwhelming the system

    for (let i = 0; i < reminders.length; i += batchSize) {
      const batch = reminders.slice(i, i + batchSize);
      const batchPromises = batch.map(reminder => this.scheduleReminder(reminder));

      try {
        await Promise.allSettled(batchPromises);
        scheduledCount += batch.length;
      } catch (error) {
        console.error(`❌ [REMINDER-SCHEDULER] Error in batch ${Math.floor(i / batchSize) + 1}:`, error.message);
      }
    }

    return scheduledCount;
  }

  /**
   * Schedule a single reminder to be sent at its specified time using Redis-based delayed jobs
   */
  async scheduleReminder(reminder: Reminder): Promise<void> {
    const startTime = Date.now();

    try {
      const delayMs = this.calculateReminderDelay(reminder.time);
      const payload = this.createNotificationPayload([reminder]);
      const jobId = `reminder_${reminder.id}_${Date.now()}`;

      // Use Redis-based delayed job service for reliable scheduling
      const success = await this.delayedJobService.scheduleJob(
        jobId,
        payload,
        NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE,
        delayMs
      );

      if (success) {
        this.performanceMonitor.trackReminderScheduled();
        this.performanceMonitor.trackResponseTime(Date.now() - startTime);

        const delayMinutes = Math.round(delayMs / 60000);
        console.log(`⏰ [REMINDER-SCHEDULER] Scheduled "${reminder.title}" for ${delayMinutes}min delay`);
      } else {
        this.performanceMonitor.trackError();
        console.error(`❌ [REMINDER-SCHEDULER] Failed to schedule "${reminder.title}"`);
      }
    } catch (error) {
      this.performanceMonitor.trackError();
      this.performanceMonitor.trackResponseTime(Date.now() - startTime);
      console.error(`❌ [REMINDER-SCHEDULER] Failed to schedule "${reminder.title}":`, error.message);
    }
  }

  /**
   * Calculate delay in milliseconds until reminder time
   */
  private calculateReminderDelay(reminderTime: string): number {
    const now = new Date();
    const currentTimeMs = (now.getHours() * 60 * 60 * 1000) + (now.getMinutes() * 60 * 1000);
    const reminderTimeMs = this.parseTimeToMs(reminderTime);

    let delayMs = reminderTimeMs - currentTimeMs;
    if (delayMs <= 0) {
      // If time has passed for today, schedule for tomorrow
      delayMs += 24 * 60 * 60 * 1000;
    }

    return delayMs;
  }

}
